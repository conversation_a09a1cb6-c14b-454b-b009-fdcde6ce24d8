#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的小红书评论机器人逻辑
"""

import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from xhs_comment_bot_enhanced import XHSCommentBotEnhanced

def test_duplicate_post_handling():
    """测试重复帖子处理逻辑"""
    print("开始测试重复帖子处理逻辑...")
    
    # 创建临时文件用于测试
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        temp_file = f.name
        # 预先写入一些已评论的帖子ID (对应索引0和1的帖子)
        f.write("post_000\n")
        f.write("post_001\n")
    
    try:
        # 创建测试配置
        test_config = {
            'base_url': 'https://www.xiaohongshu.com',
            'browser': {
                'headless': True,
                'width': 1280,
                'height': 720,
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            'files': {
                'cookie_file': 'cookies.json',
                'commented_posts_file': temp_file
            },
            'selectors': {
                'search_input': 'input[placeholder*="搜索"]',
                'note_item': '.note-item, .feed-item',
                'comment_input': 'textarea, input[placeholder*="评论"]'
            },
            'timing': {
                'page_load_delay': 2,
                'comment_delay_min': 3,
                'comment_delay_max': 8
            }
        }
        
        # 创建机器人实例
        bot = XHSCommentBotEnhanced(test_config)
        
        # 模拟浏览器和页面
        bot.browser = Mock()
        bot.page = Mock()
        bot.playwright = Mock()
        
        # 模拟页面URL返回不同的帖子ID - 为每个帖子索引分配固定的URL
        def get_post_url(post_index):
            post_id = f"post_{post_index:03d}"
            return f'https://www.xiaohongshu.com/explore/{post_id}'

        # 模拟当前访问的帖子索引
        current_post_index = [0]

        def mock_url():
            return get_post_url(current_post_index[0])

        bot.page.url = property(lambda self: mock_url())

        # 模拟点击帖子时更新当前帖子索引
        original_click_post = bot.click_post
        def mock_click_post(index):
            current_post_index[0] = index
            return True
        bot.click_post = mock_click_post
        
        # 模拟页面操作
        bot.page.locator.return_value.count.return_value = 10  # 10个帖子
        bot.page.locator.return_value.nth.return_value.click = Mock()
        bot.page.go_back = Mock(return_value=True)
        bot.page.goto = Mock()
        bot.page.evaluate = Mock(return_value={'isLoggedIn': True})
        
        # 模拟评论操作
        def mock_add_comment(comment):
            print(f"模拟添加评论: {comment}")
            return True
            
        bot.add_comment = mock_add_comment
        
        # 模拟延时
        bot.random_delay = Mock()
        
        # 测试场景：有4个帖子，前2个已评论过，后2个是新的
        print("\n测试场景：搜索到10个帖子，尝试评论2个")
        print("预期行为：跳过已评论的帖子，找到新帖子进行评论")
        
        # 执行测试（模拟部分流程）
        bot.stats['start_time'] = 0
        bot.stats['end_time'] = 0
        
        # 模拟主要逻辑
        available_posts = 10
        post_count = 2
        successful_comments = 0
        attempted_posts = set()
        skipped_posts = []
        
        print(f"\n开始模拟评论流程...")
        
        for i in range(post_count):
            print(f"\n--- 处理第 {i + 1}/{post_count} 个帖子 ---")
            
            post_found = False
            max_attempts = available_posts
            
            for _ in range(max_attempts):
                # 选择未尝试过的随机帖子
                remaining_posts = [idx for idx in range(available_posts) 
                                 if idx not in attempted_posts]
                
                if not remaining_posts:
                    print("没有更多未尝试的帖子")
                    break
                
                import random
                post_index = random.choice(remaining_posts)
                attempted_posts.add(post_index)
                print(f"尝试帖子索引: {post_index + 1}")
                
                # 模拟点击帖子
                bot.click_post(post_index)  # 这会更新current_post_index
                print(f"点击帖子 {post_index + 1}")

                # 模拟检查是否已评论过
                current_url = mock_url()
                print(f"当前URL: {current_url}")

                # 提取帖子ID
                import re
                match = re.search(r'/explore/([a-f0-9_]+)', current_url)
                if match:
                    post_id = match.group(1)
                    print(f"提取到帖子ID: {post_id}")
                    print(f"已评论帖子列表: {list(bot.commented_posts)}")

                    # 检查是否已评论过
                    if bot.is_post_commented(post_id):
                        print(f"帖子 {post_id} 已经评论过，跳过")
                        skipped_posts.append(post_index)
                        print("返回搜索结果页")
                        continue
                    else:
                        print(f"帖子 {post_id} 未评论过，可以评论")
                        post_found = True
                        break
                else:
                    print(f"无法从URL提取帖子ID: {current_url}")
                    continue
            
            if not post_found:
                print("无法找到更多有效帖子，结束任务")
                break
            
            # 模拟评论
            comment = "测试评论内容"
            print(f"添加评论: {comment}")
            successful_comments += 1
            
            # 模拟保存帖子ID
            if match:
                bot.save_commented_post(match.group(1))
            
            print(f"评论成功，当前成功数: {successful_comments}")
            
            if i < post_count - 1:
                print("返回搜索结果页")
        
        print(f"\n=== 测试结果 ===")
        print(f"成功评论数: {successful_comments}")
        print(f"跳过的帖子: {len(skipped_posts)}")
        print(f"尝试的帖子总数: {len(attempted_posts)}")
        
        # 验证结果
        assert successful_comments > 0, "应该至少成功评论一个帖子"
        assert len(skipped_posts) > 0, "应该跳过一些已评论的帖子"
        print("✅ 测试通过！修复逻辑正确工作")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

if __name__ == "__main__":
    test_duplicate_post_handling()
    print("\n🎉 所有测试完成！")
